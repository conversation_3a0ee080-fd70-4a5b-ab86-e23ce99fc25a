import { useEffect, useCallback } from 'react';

export interface KeyboardShortcut {
  key: string;
  ctrlKey?: boolean;
  altKey?: boolean;
  metaKey?: boolean;
  shiftKey?: boolean;
  callback: (event: KeyboardEvent) => void;
  preventDefault?: boolean;
  description?: string;
}

export interface UseKeyboardShortcutsOptions {
  enabled?: boolean;
  target?: EventTarget | null;
}

export function useKeyboardShortcuts(
  shortcuts: KeyboardShortcut[],
  options: UseKeyboardShortcutsOptions = {}
) {
  const { enabled = true, target = document } = options;

  const handleKeyDown = useCallback(
    (event: KeyboardEvent) => {
      if (!enabled) return;

      // Find matching shortcut
      const matchingShortcut = shortcuts.find(shortcut => {
        const keyMatches = shortcut.key.toLowerCase() === event.key.toLowerCase();
        const ctrlMatches = (shortcut.ctrlKey ?? false) === event.ctrlKey;
        const altMatches = (shortcut.altKey ?? false) === event.altKey;
        const metaMatches = (shortcut.metaKey ?? false) === event.metaKey;
        const shiftMatches = (shortcut.shiftKey ?? false) === event.shiftKey;

        return keyMatches && ctrlMatches && altMatches && metaMatches && shiftMatches;
      });

      if (matchingShortcut) {
        if (matchingShortcut.preventDefault !== false) {
          event.preventDefault();
        }
        matchingShortcut.callback(event);
      }
    },
    [shortcuts, enabled]
  );

  useEffect(() => {
    if (!target || !enabled) return;

    target.addEventListener('keydown', handleKeyDown as EventListener);
    return () => {
      target.removeEventListener('keydown', handleKeyDown as EventListener);
    };
  }, [target, enabled, handleKeyDown]);
}

// Hook specifically for search functionality
export function useSearchShortcuts(onOpenSearch: () => void, enabled = true) {
  const shortcuts: KeyboardShortcut[] = [
    {
      key: 'k',
      ctrlKey: true,
      callback: onOpenSearch,
      description: 'Open search (Ctrl+K)',
    },
    {
      key: '/',
      callback: onOpenSearch,
      description: 'Open search (/)',
    },
  ];

  // Also handle any printable character to open search
  const handleAnyKey = useCallback(
    (event: KeyboardEvent) => {
      if (!enabled) return;

      // Skip if modifier keys are pressed (except shift for uppercase)
      if (event.ctrlKey || event.altKey || event.metaKey) return;

      // Skip if target is an input element
      const target = event.target as HTMLElement;
      if (
        target.tagName === 'INPUT' ||
        target.tagName === 'TEXTAREA' ||
        target.contentEditable === 'true'
      ) {
        return;
      }

      // Skip special keys
      const specialKeys = [
        'Tab',
        'Enter',
        'Escape',
        'ArrowUp',
        'ArrowDown',
        'ArrowLeft',
        'ArrowRight',
        'Backspace',
        'Delete',
        'Home',
        'End',
        'PageUp',
        'PageDown',
        'F1', 'F2', 'F3', 'F4', 'F5', 'F6',
        'F7', 'F8', 'F9', 'F10', 'F11', 'F12',
      ];

      if (specialKeys.includes(event.key)) return;

      // If it's a printable character, open search
      if (event.key.length === 1) {
        event.preventDefault();
        onOpenSearch();
      }
    },
    [onOpenSearch, enabled]
  );

  useKeyboardShortcuts(shortcuts, { enabled });

  useEffect(() => {
    if (!enabled) return;

    document.addEventListener('keydown', handleAnyKey);
    return () => {
      document.removeEventListener('keydown', handleAnyKey);
    };
  }, [handleAnyKey, enabled]);
}

// Hook for navigation shortcuts
export function useNavigationShortcuts(enabled = true) {
  const shortcuts: KeyboardShortcut[] = [
    {
      key: 'Escape',
      callback: () => {
        // Close any open modals/overlays
        const event = new CustomEvent('closeOverlays');
        document.dispatchEvent(event);
      },
      description: 'Close overlays (Escape)',
    },
  ];

  useKeyboardShortcuts(shortcuts, { enabled });
}
