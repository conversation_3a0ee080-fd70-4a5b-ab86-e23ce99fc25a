import React, { useState } from 'react';
import { useThemeStore } from '../../stores/themeStore';
import { Input, Button } from '../ui';

export interface AccessRequestFormProps {
  onSubmit?: (data: AccessRequestData) => void;
  onBack?: () => void;
  loading?: boolean;
  error?: string;
  className?: string;
  'data-testid'?: string;
}

export interface AccessRequestData {
  fullName: string;
  email: string;
  phone: string;
  countryCode: string;
  company?: string;
  jobTitle?: string;
  message: string;
}

export function AccessRequestForm({
  onSubmit,
  onBack,
  loading = false,
  error,
  className = '',
  'data-testid': testId,
}: AccessRequestFormProps) {
  const { colors } = useThemeStore();
  const [formData, setFormData] = useState<AccessRequestData>({
    fullName: '',
    email: '',
    phone: '',
    countryCode: '+1',
    company: '',
    jobTitle: '',
    message: '',
  });
  const [fieldErrors, setFieldErrors] = useState<Record<string, string>>({});

  const countryCodes = [
    { code: '+1', country: 'US/CA', flag: '🇺🇸' },
    { code: '+44', country: 'UK', flag: '🇬🇧' },
    { code: '+91', country: 'IN', flag: '🇮🇳' },
    { code: '+86', country: 'CN', flag: '🇨🇳' },
    { code: '+49', country: 'DE', flag: '🇩🇪' },
    { code: '+33', country: 'FR', flag: '🇫🇷' },
    { code: '+81', country: 'JP', flag: '🇯🇵' },
    { code: '+61', country: 'AU', flag: '🇦🇺' },
    { code: '+55', country: 'BR', flag: '🇧🇷' },
    { code: '+7', country: 'RU', flag: '🇷🇺' },
  ];

  const validateForm = () => {
    const errors: Record<string, string> = {};

    if (!formData.fullName.trim()) {
      errors.fullName = 'Full name is required';
    } else if (formData.fullName.trim().length < 2) {
      errors.fullName = 'Full name must be at least 2 characters';
    }

    if (!formData.email) {
      errors.email = 'Email is required';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      errors.email = 'Please enter a valid email address';
    }

    if (!formData.phone) {
      errors.phone = 'Phone number is required';
    } else if (!/^\d{10,15}$/.test(formData.phone.replace(/\D/g, ''))) {
      errors.phone = 'Please enter a valid phone number';
    }

    if (!formData.message.trim()) {
      errors.message = 'Message is required';
    } else if (formData.message.trim().length < 10) {
      errors.message = 'Message must be at least 10 characters';
    }

    setFieldErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (validateForm() && onSubmit) {
      onSubmit(formData);
    }
  };

  const handleInputChange =
    (field: keyof AccessRequestData) =>
    (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
      setFormData(prev => ({ ...prev, [field]: e.target.value }));
      // Clear field error when user starts typing
      if (fieldErrors[field]) {
        setFieldErrors(prev => ({ ...prev, [field]: '' }));
      }
    };

  const UserIcon = () => (
    <svg
      className="w-5 h-5"
      fill="none"
      stroke="currentColor"
      viewBox="0 0 24 24"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
      />
    </svg>
  );

  const EmailIcon = () => (
    <svg
      className="w-5 h-5"
      fill="none"
      stroke="currentColor"
      viewBox="0 0 24 24"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207"
      />
    </svg>
  );

  const PhoneIcon = () => (
    <svg
      className="w-5 h-5"
      fill="none"
      stroke="currentColor"
      viewBox="0 0 24 24"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
      />
    </svg>
  );

  const BuildingIcon = () => (
    <svg
      className="w-5 h-5"
      fill="none"
      stroke="currentColor"
      viewBox="0 0 24 24"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"
      />
    </svg>
  );

  const BriefcaseIcon = () => (
    <svg
      className="w-5 h-5"
      fill="none"
      stroke="currentColor"
      viewBox="0 0 24 24"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2-2v2m8 0H8m8 0v2a2 2 0 01-2 2H10a2 2 0 01-2-2V6"
      />
    </svg>
  );

  const MessageIcon = () => (
    <svg
      className="w-5 h-5"
      fill="none"
      stroke="currentColor"
      viewBox="0 0 24 24"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"
      />
    </svg>
  );

  return (
    <form
      onSubmit={handleSubmit}
      className={`space-y-6 ${className}`}
      data-testid={testId}
    >
      {error && (
        <div
          className="p-4 rounded-lg border text-sm"
          style={{
            backgroundColor: `${colors.error}10`,
            borderColor: colors.error,
            color: colors.error,
          }}
        >
          {error}
        </div>
      )}

      <div className="text-center mb-6">
        <div
          className="w-16 h-16 mx-auto rounded-full flex items-center justify-center mb-4"
          style={{ backgroundColor: `${colors.primary}20` }}
        >
          <UserIcon />
        </div>
        <h3
          className="text-lg font-semibold mb-2"
          style={{ color: colors.text }}
        >
          Request Access
        </h3>
        <p className="text-sm" style={{ color: colors.mutedForeground }}>
          Fill out the form below and we'll review your request for access to
          our ERP system.
        </p>
      </div>

      {/* Full Name */}
      <Input
        type="text"
        label="Full Name *"
        placeholder="Enter your full name"
        value={formData.fullName}
        onChange={handleInputChange('fullName')}
        error={fieldErrors.fullName}
        startIcon={<UserIcon />}
        fullWidth
        disabled={loading}
        data-testid="access-fullname"
      />

      {/* Email */}
      <Input
        type="email"
        label="Email Address *"
        placeholder="Enter your email"
        value={formData.email}
        onChange={handleInputChange('email')}
        error={fieldErrors.email}
        startIcon={<EmailIcon />}
        fullWidth
        disabled={loading}
        data-testid="access-email"
      />

      {/* Phone Number */}
      <div>
        <label
          className="block text-sm font-medium mb-2"
          style={{ color: colors.text }}
        >
          Phone Number *
        </label>
        <div className="flex gap-2">
          <select
            value={formData.countryCode}
            onChange={e =>
              setFormData(prev => ({ ...prev, countryCode: e.target.value }))
            }
            className="px-3 py-2 border rounded-lg min-w-[120px]"
            style={{
              borderColor: colors.border,
              backgroundColor: colors.input,
              color: colors.inputForeground,
            }}
            disabled={loading}
          >
            {countryCodes.map(({ code, country: _country, flag }) => (
              <option key={code} value={code}>
                {flag} {code}
              </option>
            ))}
          </select>
          <Input
            type="tel"
            placeholder="Enter phone number"
            value={formData.phone}
            onChange={e =>
              setFormData(prev => ({
                ...prev,
                phone: e.target.value.replace(/\D/g, ''),
              }))
            }
            error={fieldErrors.phone}
            startIcon={<PhoneIcon />}
            fullWidth
            disabled={loading}
            data-testid="access-phone"
          />
        </div>
      </div>

      {/* Company (Optional) */}
      <Input
        type="text"
        label="Company"
        placeholder="Enter your company name (optional)"
        value={formData.company}
        onChange={handleInputChange('company')}
        startIcon={<BuildingIcon />}
        fullWidth
        disabled={loading}
        data-testid="access-company"
      />

      {/* Job Title (Optional) */}
      <Input
        type="text"
        label="Job Title"
        placeholder="Enter your job title (optional)"
        value={formData.jobTitle}
        onChange={handleInputChange('jobTitle')}
        startIcon={<BriefcaseIcon />}
        fullWidth
        disabled={loading}
        data-testid="access-jobtitle"
      />

      {/* Message */}
      <div>
        <label
          className="block text-sm font-medium mb-2"
          style={{ color: colors.text }}
        >
          Message *
        </label>
        <div className="relative">
          <div
            className="absolute top-3 left-3"
            style={{ color: colors.mutedForeground }}
          >
            <MessageIcon />
          </div>
          <textarea
            placeholder="Please describe your business needs and why you need access to our ERP system..."
            value={formData.message}
            onChange={handleInputChange('message')}
            rows={4}
            className="w-full pl-10 pr-4 py-3 border rounded-lg focus:outline-none focus:ring-2 resize-none"
            style={
              {
                borderColor: colors.border,
                backgroundColor: colors.input,
                color: colors.inputForeground,
                '--tw-ring-color': colors.ring,
              } as React.CSSProperties
            }
            disabled={loading}
            data-testid="access-message"
          />
        </div>
        {fieldErrors.message && (
          <p className="mt-1 text-sm" style={{ color: colors.error }}>
            {fieldErrors.message}
          </p>
        )}
      </div>

      {/* Privacy Notice */}
      <div className="p-4 rounded-lg" style={{ backgroundColor: colors.muted }}>
        <p className="text-xs" style={{ color: colors.mutedForeground }}>
          <strong>Privacy Notice:</strong> Your information will be used solely
          for processing your access request. We will contact you within 2-3
          business days regarding your application status.
        </p>
      </div>

      <div className="flex gap-3">
        <Button
          type="button"
          variant="outline"
          onClick={onBack}
          disabled={loading}
        >
          Back
        </Button>
        <Button
          type="submit"
          variant="primary"
          loading={loading}
          disabled={loading}
          fullWidth
          data-testid="submit-access-request"
        >
          Submit Request
        </Button>
      </div>
    </form>
  );
}
