import React from 'react';
import { useThemeStore } from '../../stores/themeStore';

export interface SocialLoginButtonsProps {
  onGoogleLogin?: () => void;
  onFacebookLogin?: () => void;
  onLinkedInLogin?: () => void;
  onMicrosoftLogin?: () => void;
  loading?: boolean;
  disabled?: boolean;
  layout?: 'vertical' | 'horizontal' | 'grid';
  showLabels?: boolean;
  className?: string;
  'data-testid'?: string;
}

export function SocialLoginButtons({
  onGoogleLogin,
  onFacebookLogin,
  onLinkedInLogin,
  onMicrosoftLogin,
  loading = false,
  disabled = false,
  layout = 'vertical',
  showLabels = true,
  className = '',
  'data-testid': testId,
}: SocialLoginButtonsProps) {
  const { colors } = useThemeStore();

  const GoogleIcon = () => (
    <svg className="w-5 h-5" viewBox="0 0 24 24">
      <path
        fill="#4285F4"
        d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
      />
      <path
        fill="#34A853"
        d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
      />
      <path
        fill="#FBBC05"
        d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
      />
      <path
        fill="#EA4335"
        d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
      />
    </svg>
  );

  const FacebookIcon = () => (
    <svg className="w-5 h-5" fill="#1877F2" viewBox="0 0 24 24">
      <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z" />
    </svg>
  );

  const LinkedInIcon = () => (
    <svg className="w-5 h-5" fill="#0A66C2" viewBox="0 0 24 24">
      <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z" />
    </svg>
  );

  const MicrosoftIcon = () => (
    <svg className="w-5 h-5" viewBox="0 0 24 24">
      <path fill="#F25022" d="M1 1h10v10H1z" />
      <path fill="#00A4EF" d="M13 1h10v10H13z" />
      <path fill="#7FBA00" d="M1 13h10v10H1z" />
      <path fill="#FFB900" d="M13 13h10v10H13z" />
    </svg>
  );

  const socialProviders = [
    {
      name: 'Google',
      icon: GoogleIcon,
      onClick: onGoogleLogin,
      bgColor: '#ffffff',
      textColor: '#1f2937',
      borderColor: '#e5e7eb',
      hoverBg: '#f9fafb',
    },
    {
      name: 'Facebook',
      icon: FacebookIcon,
      onClick: onFacebookLogin,
      bgColor: '#1877F2',
      textColor: '#ffffff',
      borderColor: '#1877F2',
      hoverBg: '#166fe5',
    },
    {
      name: 'LinkedIn',
      icon: LinkedInIcon,
      onClick: onLinkedInLogin,
      bgColor: '#0A66C2',
      textColor: '#ffffff',
      borderColor: '#0A66C2',
      hoverBg: '#095ba1',
    },
    {
      name: 'Microsoft',
      icon: MicrosoftIcon,
      onClick: onMicrosoftLogin,
      bgColor: '#ffffff',
      textColor: '#1f2937',
      borderColor: '#e5e7eb',
      hoverBg: '#f9fafb',
    },
  ];

  const getLayoutClasses = () => {
    switch (layout) {
      case 'horizontal':
        return 'flex flex-row gap-3';
      case 'grid':
        return 'grid grid-cols-2 gap-3';
      case 'vertical':
      default:
        return 'flex flex-col gap-3';
    }
  };

  const getButtonClasses = (_provider: (typeof socialProviders)[0]) => {
    const baseClasses = `
      flex items-center justify-center
      px-4 py-3
      border rounded-lg
      font-medium text-sm
      transition-all duration-200
      focus:outline-none focus:ring-2 focus:ring-offset-2
      disabled:opacity-50 disabled:cursor-not-allowed
      hover:shadow-md
    `.trim();

    const sizeClasses =
      layout === 'grid' && !showLabels ? 'w-12 h-12 p-0' : 'min-h-[48px]';

    return `${baseClasses} ${sizeClasses}`;
  };

  return (
    <div className={`${getLayoutClasses()} ${className}`} data-testid={testId}>
      {socialProviders.map(provider => {
        if (!provider.onClick) return null;

        const IconComponent = provider.icon;

        return (
          <button
            key={provider.name}
            onClick={provider.onClick}
            disabled={disabled || loading}
            className={getButtonClasses(provider)}
            style={
              {
                backgroundColor: provider.bgColor,
                color: provider.textColor,
                borderColor: provider.borderColor,
                '--hover-bg': provider.hoverBg,
                '--focus-ring': colors.primary,
              } as React.CSSProperties & {
                '--hover-bg': string;
                '--focus-ring': string;
              }
            }
            aria-label={`Sign in with ${provider.name}`}
            data-testid={`social-login-${provider.name.toLowerCase()}`}
          >
            {loading ? (
              <svg
                className="animate-spin w-5 h-5"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
              >
                <circle
                  className="opacity-25"
                  cx="12"
                  cy="12"
                  r="10"
                  stroke="currentColor"
                  strokeWidth="4"
                />
                <path
                  className="opacity-75"
                  fill="currentColor"
                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                />
              </svg>
            ) : (
              <>
                <IconComponent />
                {showLabels && (
                  <span className="ml-3">Continue with {provider.name}</span>
                )}
              </>
            )}
          </button>
        );
      })}
    </div>
  );
}

// Divider component for separating social login from other forms
export interface SocialLoginDividerProps {
  text?: string;
  className?: string;
}

export function SocialLoginDivider({
  text = 'or',
  className = '',
}: SocialLoginDividerProps) {
  const { colors } = useThemeStore();

  return (
    <div className={`relative flex items-center ${className}`}>
      <div
        className="flex-grow border-t"
        style={{ borderColor: colors.border }}
      />
      <span
        className="flex-shrink mx-4 px-2 text-sm font-medium"
        style={{ color: colors.textSecondary }}
      >
        {text}
      </span>
      <div
        className="flex-grow border-t"
        style={{ borderColor: colors.border }}
      />
    </div>
  );
}
