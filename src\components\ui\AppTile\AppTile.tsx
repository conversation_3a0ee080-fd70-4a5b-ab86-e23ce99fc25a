import React from 'react';
import { useThemeStore } from '../../../stores/themeStore';
import { cn } from '../../../utils/cn';

export interface AppTileProps {
  title: string;
  description?: string;
  icon?: React.ReactNode;
  onClick?: () => void;
  disabled?: boolean;
  className?: string;
  'data-testid'?: string;
}

const AppTile: React.FC<AppTileProps> = ({
  title,
  description,
  icon,
  onClick,
  disabled = false,
  className = '',
  'data-testid': testId,
}) => {
  const { colors, isDark } = useThemeStore();

  const handleClick = () => {
    if (!disabled && onClick) {
      onClick();
    }
  };

  const baseClasses = cn(
    'relative group cursor-pointer',
    'rounded-lg p-4 sm:p-6 transition-all duration-300 ease-out',
    'border border-transparent',
    'focus:outline-none focus:ring-2 focus:ring-primary/20 focus:ring-offset-2',
    'min-h-[120px] sm:min-h-[140px] flex flex-col justify-center',
    disabled && 'opacity-50 cursor-not-allowed pointer-events-none',
    className
  );

  // Create translucent dark background with soft shadows
  const tileStyles = {
    backgroundColor: isDark 
      ? 'rgba(30, 41, 59, 0.6)' // slate-800 with opacity
      : 'rgba(248, 250, 252, 0.8)', // slate-50 with opacity
    backdropFilter: 'blur(8px)',
    border: `1px solid ${isDark ? 'rgba(71, 85, 105, 0.3)' : 'rgba(203, 213, 225, 0.5)'}`, // slate-600/slate-300 with opacity
    boxShadow: isDark
      ? '0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2)'
      : '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
  };

  const hoverStyles = {
    transform: 'translateY(-2px)',
    boxShadow: isDark
      ? '0 10px 15px -3px rgba(0, 0, 0, 0.4), 0 4px 6px -2px rgba(0, 0, 0, 0.3)'
      : '0 10px 15px -3px rgba(0, 0, 0, 0.15), 0 4px 6px -2px rgba(0, 0, 0, 0.1)',
    backgroundColor: isDark
      ? 'rgba(30, 41, 59, 0.8)'
      : 'rgba(248, 250, 252, 0.95)',
  };

  return (
    <div
      className={baseClasses}
      style={tileStyles}
      onClick={handleClick}
      data-testid={testId}
      onMouseEnter={(e) => {
        if (!disabled) {
          Object.assign(e.currentTarget.style, hoverStyles);
        }
      }}
      onMouseLeave={(e) => {
        if (!disabled) {
          Object.assign(e.currentTarget.style, tileStyles);
        }
      }}
      tabIndex={disabled ? -1 : 0}
      role="button"
      aria-disabled={disabled}
    >
      {/* Glow effect on hover */}
      <div 
        className="absolute inset-0 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300"
        style={{
          background: isDark
            ? 'linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(147, 51, 234, 0.1))'
            : 'linear-gradient(135deg, rgba(59, 130, 246, 0.05), rgba(147, 51, 234, 0.05))',
        }}
      />

      <div className="relative z-10 flex flex-col items-center text-center space-y-2 sm:space-y-3">
        {icon && (
          <div
            className="text-xl sm:text-2xl transition-transform duration-300 group-hover:scale-110"
            style={{ color: colors.text }}
          >
            {icon}
          </div>
        )}

        <div className="space-y-1">
          <h3
            className="font-semibold text-sm sm:text-base leading-tight"
            style={{ color: colors.text }}
          >
            {title}
          </h3>

          {description && (
            <p
              className="text-xs sm:text-sm opacity-80 leading-tight line-clamp-2"
              style={{ color: colors.textSecondary }}
            >
              {description}
            </p>
          )}
        </div>
      </div>
    </div>
  );
};

export default AppTile;
