import React, { useState } from 'react';
import { useThemeStore } from '../../stores/themeStore';
import { Input, Button } from '../ui';

export interface ForgotPasswordFormProps {
  onSubmit?: (data: {
    method: 'email' | 'whatsapp' | 'mobile';
    value: string;
  }) => void;
  onBack?: () => void;
  onVerifyOTP?: (data: {
    method: 'email' | 'whatsapp' | 'mobile';
    value: string;
    otp: string;
    newPassword: string;
  }) => void;
  loading?: boolean;
  error?: string;
  step?: 'method' | 'otp' | 'reset';
  className?: string;
  'data-testid'?: string;
}

export function ForgotPasswordForm({
  onSubmit,
  onBack,
  onVerifyOTP,
  loading = false,
  error,
  step = 'method',
  className = '',
  'data-testid': testId,
}: ForgotPasswordFormProps) {
  const { colors } = useThemeStore();
  const [selectedMethod, setSelectedMethod] = useState<
    'email' | 'whatsapp' | 'mobile'
  >('email');
  const [email, setEmail] = useState('');
  const [phoneNumber, setPhoneNumber] = useState('');
  const [otp, setOTP] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [fieldErrors, setFieldErrors] = useState<Record<string, string>>({});
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  const validateInput = () => {
    const errors: Record<string, string> = {};

    if (selectedMethod === 'email') {
      if (!email) {
        errors.email = 'Email is required';
      } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
        errors.email = 'Please enter a valid email address';
      }
    } else {
      if (!phoneNumber) {
        errors.phoneNumber = 'Phone number is required';
      } else if (!/^\+?[\d\s\-()]{10,}$/.test(phoneNumber)) {
        errors.phoneNumber = 'Please enter a valid phone number';
      }
    }

    setFieldErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const validateOTP = () => {
    const errors: Record<string, string> = {};

    if (!otp) {
      errors.otp = 'Verification code is required';
    } else if (otp.length !== 6) {
      errors.otp = 'Please enter the complete 6-digit code';
    }

    setFieldErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const validatePasswords = () => {
    const errors: Record<string, string> = {};

    if (!newPassword) {
      errors.newPassword = 'New password is required';
    } else if (newPassword.length < 8) {
      errors.newPassword = 'Password must be at least 8 characters';
    } else if (!/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(newPassword)) {
      errors.newPassword =
        'Password must contain at least one uppercase letter, one lowercase letter, and one number';
    }

    if (!confirmPassword) {
      errors.confirmPassword = 'Please confirm your password';
    } else if (newPassword !== confirmPassword) {
      errors.confirmPassword = 'Passwords do not match';
    }

    setFieldErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleMethodSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (validateInput() && onSubmit) {
      const value = selectedMethod === 'email' ? email : phoneNumber;
      onSubmit({ method: selectedMethod, value });
    }
  };

  const handleResetSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (validateOTP() && validatePasswords() && onVerifyOTP) {
      const value = selectedMethod === 'email' ? email : phoneNumber;
      onVerifyOTP({ method: selectedMethod, value, otp, newPassword });
    }
  };

  const EmailIcon = () => (
    <svg
      className="w-5 h-5"
      fill="none"
      stroke="currentColor"
      viewBox="0 0 24 24"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207"
      />
    </svg>
  );

  const LockIcon = () => (
    <svg
      className="w-5 h-5"
      fill="none"
      stroke="currentColor"
      viewBox="0 0 24 24"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"
      />
    </svg>
  );

  const KeyIcon = () => (
    <svg
      className="w-5 h-5"
      fill="none"
      stroke="currentColor"
      viewBox="0 0 24 24"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z"
      />
    </svg>
  );

  const EyeIcon = () => (
    <svg
      className="w-5 h-5"
      fill="none"
      stroke="currentColor"
      viewBox="0 0 24 24"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
      />
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
      />
    </svg>
  );

  const EyeOffIcon = () => (
    <svg
      className="w-5 h-5"
      fill="none"
      stroke="currentColor"
      viewBox="0 0 24 24"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21"
      />
    </svg>
  );

  const WhatsAppIcon = () => (
    <svg className="w-5 h-5" viewBox="0 0 24 24" fill="currentColor">
      <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488" />
    </svg>
  );

  const MobileIcon = () => (
    <svg
      className="w-5 h-5"
      fill="none"
      stroke="currentColor"
      viewBox="0 0 24 24"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"
      />
    </svg>
  );

  if (step === 'method') {
    return (
      <form
        onSubmit={handleMethodSubmit}
        className={`space-y-6 ${className}`}
        data-testid={testId}
      >
        {error && (
          <div
            className="p-4 rounded-lg border text-sm"
            style={{
              backgroundColor: `${colors.error}10`,
              borderColor: colors.error,
              color: colors.error,
            }}
          >
            {error}
          </div>
        )}

        <div className="text-center mb-6">
          <div
            className="w-16 h-16 mx-auto rounded-full flex items-center justify-center mb-4"
            style={{ backgroundColor: `${colors.primary}20` }}
          >
            <KeyIcon />
          </div>
          <h3
            className="text-lg font-semibold mb-2"
            style={{ color: colors.text }}
          >
            Forgot your password?
          </h3>
          <p className="text-sm" style={{ color: colors.mutedForeground }}>
            Choose how you'd like to receive your password reset code.
          </p>
        </div>

        {/* Method Selection */}
        <div className="space-y-3">
          <label
            className="block text-sm font-medium mb-3"
            style={{ color: colors.text }}
          >
            Reset Method
          </label>

          {/* Email Option */}
          <button
            type="button"
            onClick={() => setSelectedMethod('email')}
            className="w-full p-4 rounded-lg border-2 transition-all duration-200"
            style={{
              borderColor:
                selectedMethod === 'email' ? colors.primary : colors.border,
              backgroundColor:
                selectedMethod === 'email'
                  ? `${colors.primary}10`
                  : 'transparent',
            }}
            onMouseEnter={e => {
              if (selectedMethod !== 'email') {
                e.currentTarget.style.borderColor = colors.borderSecondary;
              }
            }}
            onMouseLeave={e => {
              if (selectedMethod !== 'email') {
                e.currentTarget.style.borderColor = colors.border;
              }
            }}
          >
            <div className="flex items-center gap-3">
              <div
                className="w-10 h-10 rounded-lg flex items-center justify-center"
                style={{
                  backgroundColor:
                    selectedMethod === 'email' ? colors.primary : colors.muted,
                  color:
                    selectedMethod === 'email'
                      ? colors.primaryForeground
                      : colors.mutedForeground,
                }}
              >
                <EmailIcon />
              </div>
              <div className="text-left">
                <div className="font-medium" style={{ color: colors.text }}>
                  Email
                </div>
                <div
                  className="text-sm"
                  style={{ color: colors.mutedForeground }}
                >
                  Send reset code to your email
                </div>
              </div>
            </div>
          </button>

          {/* WhatsApp Option */}
          <button
            type="button"
            onClick={() => setSelectedMethod('whatsapp')}
            className="w-full p-4 rounded-lg border-2 transition-all duration-200"
            style={{
              borderColor:
                selectedMethod === 'whatsapp' ? colors.success : colors.border,
              backgroundColor:
                selectedMethod === 'whatsapp'
                  ? `${colors.success}10`
                  : 'transparent',
            }}
            onMouseEnter={e => {
              if (selectedMethod !== 'whatsapp') {
                e.currentTarget.style.borderColor = colors.borderSecondary;
              }
            }}
            onMouseLeave={e => {
              if (selectedMethod !== 'whatsapp') {
                e.currentTarget.style.borderColor = colors.border;
              }
            }}
          >
            <div className="flex items-center gap-3">
              <div
                className="w-10 h-10 rounded-lg flex items-center justify-center"
                style={{
                  backgroundColor:
                    selectedMethod === 'whatsapp'
                      ? colors.success
                      : colors.muted,
                  color:
                    selectedMethod === 'whatsapp'
                      ? colors.successForeground
                      : colors.mutedForeground,
                }}
              >
                <WhatsAppIcon />
              </div>
              <div className="text-left">
                <div className="font-medium" style={{ color: colors.text }}>
                  WhatsApp
                </div>
                <div
                  className="text-sm"
                  style={{ color: colors.mutedForeground }}
                >
                  Send reset code via WhatsApp
                </div>
              </div>
            </div>
          </button>

          {/* Mobile SMS Option */}
          <button
            type="button"
            onClick={() => setSelectedMethod('mobile')}
            className={`w-full p-4 rounded-lg border-2 transition-all duration-200 ${
              selectedMethod === 'mobile'
                ? 'border-purple-500 bg-purple-50 dark:bg-purple-900/20'
                : 'border-slate-200 dark:border-slate-700 hover:border-slate-300 dark:hover:border-slate-600'
            }`}
          >
            <div className="flex items-center gap-3">
              <div
                className="w-10 h-10 rounded-lg flex items-center justify-center"
                style={{
                  backgroundColor:
                    selectedMethod === 'mobile' ? colors.accent : colors.muted,
                  color:
                    selectedMethod === 'mobile'
                      ? colors.accentForeground
                      : colors.mutedForeground,
                }}
              >
                <MobileIcon />
              </div>
              <div className="text-left">
                <div className="font-medium" style={{ color: colors.text }}>
                  SMS
                </div>
                <div
                  className="text-sm"
                  style={{ color: colors.mutedForeground }}
                >
                  Send reset code via SMS
                </div>
              </div>
            </div>
          </button>
        </div>

        {/* Input Field */}
        {selectedMethod === 'email' ? (
          <Input
            type="email"
            label="Email Address"
            placeholder="Enter your email"
            value={email}
            onChange={e => setEmail(e.target.value)}
            error={fieldErrors.email}
            startIcon={<EmailIcon />}
            fullWidth
            disabled={loading}
            data-testid="forgot-email"
          />
        ) : (
          <Input
            type="tel"
            label="Phone Number"
            placeholder="Enter your phone number"
            value={phoneNumber}
            onChange={e => setPhoneNumber(e.target.value)}
            error={fieldErrors.phoneNumber}
            startIcon={
              selectedMethod === 'whatsapp' ? <WhatsAppIcon /> : <MobileIcon />
            }
            fullWidth
            disabled={loading}
            data-testid="forgot-phone"
          />
        )}

        <div className="flex gap-3">
          <Button
            type="button"
            variant="outline"
            onClick={onBack}
            disabled={loading}
          >
            Back
          </Button>
          <Button
            type="submit"
            variant="primary"
            loading={loading}
            disabled={loading}
            fullWidth
            data-testid="send-reset-code"
          >
            Send Reset Code
          </Button>
        </div>
      </form>
    );
  }

  return (
    <form
      onSubmit={handleResetSubmit}
      className={`space-y-6 ${className}`}
      data-testid={testId}
    >
      {error && (
        <div
          className="p-4 rounded-lg border text-sm"
          style={{
            backgroundColor: `${colors.error}10`,
            borderColor: colors.error,
            color: colors.error,
          }}
        >
          {error}
        </div>
      )}

      <div className="text-center mb-6">
        <p className="text-sm" style={{ color: colors.mutedForeground }}>
          Enter the verification code sent to{' '}
          <strong>{selectedMethod === 'email' ? email : phoneNumber}</strong>{' '}
          via{' '}
          {selectedMethod === 'email'
            ? 'email'
            : selectedMethod === 'whatsapp'
              ? 'WhatsApp'
              : 'SMS'}{' '}
          and your new password.
        </p>
      </div>

      {/* OTP Field */}
      <Input
        type="text"
        label="Verification Code"
        placeholder="Enter 6-digit code"
        value={otp}
        onChange={e => setOTP(e.target.value.replace(/\D/g, '').slice(0, 6))}
        error={fieldErrors.otp}
        startIcon={<KeyIcon />}
        fullWidth
        disabled={loading}
        data-testid="reset-otp"
      />

      {/* New Password */}
      <div className="relative">
        <Input
          type={showPassword ? 'text' : 'password'}
          label="New Password"
          placeholder="Enter new password"
          value={newPassword}
          onChange={e => setNewPassword(e.target.value)}
          error={fieldErrors.newPassword}
          helperText="Must be at least 8 characters with uppercase, lowercase, and number"
          startIcon={<LockIcon />}
          fullWidth
          disabled={loading}
          data-testid="new-password"
        />
        <button
          type="button"
          onClick={() => setShowPassword(!showPassword)}
          className="absolute right-3 top-9 transition-colors"
          style={{ color: colors.mutedForeground }}
          onMouseEnter={e => {
            e.currentTarget.style.color = colors.textSecondary;
          }}
          onMouseLeave={e => {
            e.currentTarget.style.color = colors.mutedForeground;
          }}
          disabled={loading}
          aria-label={showPassword ? 'Hide password' : 'Show password'}
        >
          {showPassword ? <EyeOffIcon /> : <EyeIcon />}
        </button>
      </div>

      {/* Confirm Password */}
      <div className="relative">
        <Input
          type={showConfirmPassword ? 'text' : 'password'}
          label="Confirm New Password"
          placeholder="Confirm new password"
          value={confirmPassword}
          onChange={e => setConfirmPassword(e.target.value)}
          error={fieldErrors.confirmPassword}
          startIcon={<LockIcon />}
          fullWidth
          disabled={loading}
          data-testid="confirm-password"
        />
        <button
          type="button"
          onClick={() => setShowConfirmPassword(!showConfirmPassword)}
          className="absolute right-3 top-9 transition-colors"
          style={{ color: colors.mutedForeground }}
          onMouseEnter={e => {
            e.currentTarget.style.color = colors.textSecondary;
          }}
          onMouseLeave={e => {
            e.currentTarget.style.color = colors.mutedForeground;
          }}
          disabled={loading}
          aria-label={showConfirmPassword ? 'Hide password' : 'Show password'}
        >
          {showConfirmPassword ? <EyeOffIcon /> : <EyeIcon />}
        </button>
      </div>

      <div className="flex gap-3">
        <Button
          type="button"
          variant="outline"
          onClick={onBack}
          disabled={loading}
        >
          Back
        </Button>
        <Button
          type="submit"
          variant="primary"
          loading={loading}
          disabled={loading}
          fullWidth
          data-testid="reset-password"
        >
          Reset Password
        </Button>
      </div>
    </form>
  );
}
